package handlers

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"log/slog"
	"net/http"
	"strings"

	"github.com/information-sharing-networks/signalsd/app/internal/logger"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/client"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/config"
	uierrors "github.com/information-sharing-networks/signalsd/app/internal/ui/errors"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/templates"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)

type HandlerService struct {
	AuthService *auth.AuthService
	ApiClient   *client.Client
	Config      *config.Config
}

// RenderError displays an error message inline to the user
func (h *HandlerService) RenderError(w http.ResponseWriter, r *http.Request, error error) {
	component := templates.ErrorAlert(error.Error())
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger := logger.ContextRequestLogger(r.Context())
		reqLogger.Error("Failed to render error", slog.String("error", err.Error()))
	}
}

// HandleHome handles the root path and redirects to the dashboard if authenticated, login if not
func (h *HandlerService) HandleHome(w http.ResponseWriter, r *http.Request) {
	status := h.AuthService.CheckTokenStatus(r)

	switch status {
	case auth.TokenValid:
		http.Redirect(w, r, "/dashboard", http.StatusSeeOther)
	case auth.TokenExpired, auth.TokenInvalid, auth.TokenMissing:
		h.RedirectToLogin(w, r)
	}
}

func (h *HandlerService) HandleLogin(w http.ResponseWriter, r *http.Request) {
	// Render login page
	component := templates.LoginPage()
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger := logger.ContextRequestLogger(r.Context())
		reqLogger.Error("Failed to render login page", slog.String("error", err.Error()))
	}
}

// HandleLoginPost authenticates the user and adds authentication cookies to the response
func (h *HandlerService) HandleLoginPost(w http.ResponseWriter, r *http.Request) {
	email := r.FormValue("email")
	password := r.FormValue("password")

	reqLogger := logger.ContextRequestLogger(r.Context())

	// Authenticate with signalsd API using the client
	accessTokenDetails, refreshTokenCookie, err := h.ApiClient.Login(email, password)
	if err != nil {
		reqLogger.Error("Authentication failed", slog.String("error", err.Error()))
		h.RenderError(w, r, err) // use the API error message for login failures.
		return
	}

	// Set all authentication cookies using shared method
	if err := h.AuthService.SetAuthCookies(w, accessTokenDetails, refreshTokenCookie, h.Config.Environment); err != nil {
		reqLogger.Error("Failed to set authentication cookies", slog.String("error", err.Error()))
		h.RenderError(w, r, uierrors.NewUIError(http.StatusInternalServerError, nil))
		return
	}

	// Login successful - add account log attribute to context so it is included in the final request log
	_ = logger.ContextWithLogAttrs(r.Context(),
		slog.String("account_id", accessTokenDetails.AccountID),
	)

	// redirect to dashboard
	w.Header().Set("HX-Redirect", "/dashboard")
	w.WriteHeader(http.StatusOK)
}

func (h *HandlerService) HandleRegister(w http.ResponseWriter, r *http.Request) {
	// Render registration page
	component := templates.RegisterPage()
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger := logger.ContextRequestLogger(r.Context())
		reqLogger.Error("Failed to render registration page", slog.String("error", err.Error()))
	}
}

// HandleRegisterPost processes user registration
func (h *HandlerService) HandleRegisterPost(w http.ResponseWriter, r *http.Request) {
	email := r.FormValue("email")
	password := r.FormValue("password")
	confirmPassword := r.FormValue("confirm_password")
	reqLogger := logger.ContextRequestLogger(r.Context())

	if email == "" || password == "" || confirmPassword == "" {
		h.RenderError(w, r, uierrors.NewUIError(http.StatusBadRequest, nil))
		return
	}

	if password != confirmPassword {
		h.RenderError(w, r, uierrors.NewUIError(http.StatusBadRequest, nil))
		return
	}

	// Register user with signalsd API
	err := h.ApiClient.RegisterUser(email, password)
	if err != nil {
		reqLogger.Error("Registration failed", slog.String("error", err.Error()))
		h.RenderError(w, r, err)
		return
	}

	// Registration successful - show success message and redirect to login after delay
	w.Header().Set("HX-Trigger-After-Settle", "registrationSuccess")
	component := templates.RegistrationSuccess()
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render registration success", slog.String("error", err.Error()))
	}
}

func (h *HandlerService) HandleLogout(w http.ResponseWriter, r *http.Request) {
	h.AuthService.ClearAuthCookies(w, h.Config.Environment)

	// Redirect to login page
	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/login")
		w.WriteHeader(http.StatusOK)
	} else {
		http.Redirect(w, r, "/login", http.StatusSeeOther)
	}
}

func (h *HandlerService) HandleDashboard(w http.ResponseWriter, r *http.Request) {
	component := templates.DashboardPage()
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger := logger.ContextRequestLogger(r.Context())
		reqLogger.Error("Failed to render dashboard page", slog.String("error", err.Error()))
	}
}

// HandleSignalSearch renders the signal search page
// ISN access is validated by RequireIsnAccess middleware
func (h *HandlerService) HandleSignalSearch(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	// Get ISN permissions from cookie - middleware ensures this exists
	isnPerms, err := h.AuthService.GetIsnPermsFromCookie(r)
	if err != nil {
		reqLogger.Error("failed to read IsnPerms from cookie", slog.String("error", err.Error()))
		return
	}

	// Convert permissions to ISN list for dropdown
	isns := make([]types.IsnDropdown, 0, len(isnPerms))
	for isnSlug := range isnPerms {
		isns = append(isns, types.IsnDropdown{
			Slug:    isnSlug,
			IsInUse: true,
		})
	}

	// Render search page
	component := templates.SignalSearchPage(isns, isnPerms, nil)
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render signal search page", slog.String("error", err.Error()))
	}
}

// HandleGetSignalTypes handles the form submission to get signal types for the selected ISN
func (h *HandlerService) HandleGetSignalTypes(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	isnSlug := r.FormValue("isn_slug")
	if isnSlug == "" {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// Get permissions data from cookie
	permsCookie, err := r.Cookie(config.IsnPermsCookieName)
	if err != nil {
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Decode base64 cookie value
	decodedPerms, err := base64.StdEncoding.DecodeString(permsCookie.Value)
	if err != nil {
		reqLogger.Error("Failed to decode permissions cookie in signal types handler",
			slog.String("error", err.Error()),
			slog.String("cookie_value", permsCookie.Value))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	var perms map[string]types.IsnPerm
	if err := json.Unmarshal(decodedPerms, &perms); err != nil {
		reqLogger.Error("Failed to parse permissions JSON in signal types handler",
			slog.String("error", err.Error()),
			slog.String("json_data", string(decodedPerms)))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Get signal types for the selected ISN
	isnPerm, exists := perms[isnSlug]
	if !exists {
		w.WriteHeader(http.StatusForbidden)
		return
	}

	// Parse signal type paths to extract unique signal types
	signalTypeMap := make(map[string]bool)
	for _, path := range isnPerm.SignalTypePaths {
		// Path format: "signal-type-slug/v1.0.0"
		parts := strings.Split(path, "/v")
		if len(parts) == 2 {
			signalTypeMap[parts[0]] = true
		}
	}

	// Convert to slice of SignalTypeDropdown
	signalTypes := make([]types.SignalTypeDropdown, 0, len(signalTypeMap))
	for signalType := range signalTypeMap {
		signalTypes = append(signalTypes, types.SignalTypeDropdown{
			Slug: signalType,
		})
	}

	// Render signal types dropdown options
	component := templates.SignalTypeOptions(signalTypes)
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render signal type options", slog.String("error", err.Error()))
	}
}

func (h *HandlerService) HandleGetSignalVersions(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	isnSlug := r.FormValue("isn_slug")
	signalTypeSlug := r.FormValue("signal_type_slug")
	if isnSlug == "" || signalTypeSlug == "" {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// Get permissions data from cookie
	permsCookie, err := r.Cookie(config.IsnPermsCookieName)
	if err != nil {
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Decode base64 cookie value
	decodedPerms, err := base64.StdEncoding.DecodeString(permsCookie.Value)
	if err != nil {
		reqLogger.Error("Failed to decode permissions cookie in versions handler",
			slog.String("error", err.Error()),
			slog.String("cookie_value", permsCookie.Value))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	var perms map[string]types.IsnPerm
	if err := json.Unmarshal(decodedPerms, &perms); err != nil {
		reqLogger.Error("Failed to parse permissions JSON in versions handler",
			slog.String("error", err.Error()),
			slog.String("json_data", string(decodedPerms)))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Get signal types for the selected ISN
	isnPerm, exists := perms[isnSlug]
	if !exists {
		w.WriteHeader(http.StatusForbidden)
		return
	}

	// Find versions for the specific signal type
	versions := make([]types.VersionDropdown, 0)
	for _, path := range isnPerm.SignalTypePaths {
		// Path format: "signal-type-slug/v1.0.0"
		parts := strings.Split(path, "/v")
		if len(parts) == 2 && parts[0] == signalTypeSlug {
			versions = append(versions, types.VersionDropdown{
				Version: parts[1],
			})
		}
	}

	// Render version dropdown options
	component := templates.VersionOptions(versions)
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render version options", slog.String("error", err.Error()))
	}
}

func (h *HandlerService) HandleSearchSignals(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	// Parse search parameters
	params := types.SignalSearchParams{
		IsnSlug:                 r.FormValue("isn_slug"),
		SignalTypeSlug:          r.FormValue("signal_type_slug"),
		SemVer:                  r.FormValue("sem_ver"),
		StartDate:               r.FormValue("start_date"),
		EndDate:                 r.FormValue("end_date"),
		AccountID:               r.FormValue("account_id"),
		SignalID:                r.FormValue("signal_id"),
		LocalRef:                r.FormValue("local_ref"),
		IncludeWithdrawn:        r.FormValue("include_withdrawn") == "true",
		IncludeCorrelated:       r.FormValue("include_correlated") == "true",
		IncludePreviousVersions: r.FormValue("include_previous_versions") == "true",
	}

	// Validate required parameters
	if params.IsnSlug == "" || params.SignalTypeSlug == "" || params.SemVer == "" {
		h.RenderError(w, r, uierrors.NewUIError(http.StatusBadRequest, nil))
		return
	}

	//todo make helper
	// Get user permissions to validate ISN access and determine visibility
	isnPerm, err := h.AuthService.CheckIsnPermission(r, params.IsnSlug)
	if err != nil {
		h.RenderError(w, r, uierrors.NewUIError(0, err))
		return
	}

	// Get access token from cookie
	accessTokenCookie, err := r.Cookie(config.AccessTokenCookieName)
	if err != nil {
		reqLogger.Error("Access token not found", slog.String("error", err.Error()))
		h.RenderError(w, r, uierrors.NewUIError(http.StatusUnauthorized, err))
		return
	}
	accessToken := accessTokenCookie.Value

	// Perform search using ISN visibility to determine endpoint
	searchResp, err := h.ApiClient.SearchSignals(accessToken, params, isnPerm.Visibility)
	if err != nil {
		reqLogger.Error("Signal search failed", slog.String("error", err.Error()))
		h.RenderError(w, r, err)
		return
	}

	// Render search results
	component := templates.SearchResults(*searchResp)
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render search results", slog.String("error", err.Error()))
	}
}

// HandleAdminDashboard renders the main admin dashboard page
// Access control is handled by RequireAdminAccess middleware
func (h *HandlerService) HandleAdminDashboard(w http.ResponseWriter, r *http.Request) {
	// Render admin dashboard - access is validated by middleware
	component := templates.AdminDashboardPage()
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger := logger.ContextRequestLogger(r.Context())
		reqLogger.Error("Failed to render admin dashboard", slog.String("error", err.Error()))
	}
}

// HandleIsnAccountsAdmin renders the ISN accounts administration page
func (h *HandlerService) HandleIsnAccountsAdmin(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	// Get user permissions from cookie
	isnPerms, err := h.AuthService.GetIsnPermsFromCookie(r)
	if err != nil {
		reqLogger.Error("failed to read IsnPerms from cookie", slog.String("error", err.Error()))
		return
	}

	// Convert permissions to ISN list for dropdown (only ISNs where user has admin rights)
	var isns []types.IsnDropdown
	isns = make([]types.IsnDropdown, 0, len(isnPerms))
	for isnSlug, perm := range isnPerms {
		// Only show ISNs where user has write permission (admins/owners)
		if perm.Permission == "write" {
			isns = append(isns, types.IsnDropdown{
				Slug:    isnSlug,
				IsInUse: true,
			})
		}
	}

	// Render admin page
	component := templates.IsnAccountsAdminPage(isns)
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render ISN accounts admin page", slog.String("error", err.Error()))
	}
}

// HandleAddIsnAccount handles the form submission to add an account to an ISN
func (h *HandlerService) HandleAddIsnAccount(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	// Parse form data
	isnSlug := r.FormValue("isn_slug")
	accountEmail := r.FormValue("account_email")
	permission := r.FormValue("permission")

	// Validate required fields
	if isnSlug == "" || accountEmail == "" || permission == "" {
		h.RenderError(w, r, errors.New("please fill in all fields"))
		return
	}

	// Get access token from cookie
	accessTokenCookie, err := r.Cookie(config.AccessTokenCookieName)
	if err != nil {
		reqLogger.Error("Failed to read access token cookie", slog.String("component", "templates.handleAddIsnAccount"), slog.String("error", err.Error()))
		h.RenderError(w, r, uierrors.NewUIError(http.StatusUnauthorized, err))
		return
	}
	accessToken := accessTokenCookie.Value

	// Call the API to add the account to the ISN
	err = h.ApiClient.AddAccountToIsn(accessToken, isnSlug, accountEmail, permission)
	if err != nil {
		reqLogger.Error("Failed to add account to ISN", slog.String("component", "templates.handleAddIsnAccount"), slog.String("error", err.Error()))

		h.RenderError(w, r, err)
		return
	}

	// Success response
	component := templates.SuccessAlert("Account successfully added to ISN")
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render success message", slog.String("error", err.Error()))
	}
}

// Helper method for redirecting to login
func (h *HandlerService) RedirectToLogin(w http.ResponseWriter, r *http.Request) {
	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/login")
		w.WriteHeader(http.StatusOK)
	} else {
		http.Redirect(w, r, "/login", http.StatusSeeOther)
	}
}

// HandleAccessDenied handles access denied for both HTMX and direct requests
// Always redirects to an access denied page for consistent UX
func (h *HandlerService) HandleAccessDenied(w http.ResponseWriter, r *http.Request, pageTitle, message string) {
	if r.Header.Get("HX-Request") == "true" {
		// HTMX request - redirect to access denied page
		w.Header().Set("HX-Redirect", "/access-denied?title="+pageTitle+"&message="+message)
	} else {
		// Direct navigation - render access denied page
		component := templates.AccessDeniedPage(pageTitle, message)
		if err := component.Render(r.Context(), w); err != nil {
			reqLogger := logger.ContextRequestLogger(r.Context())
			reqLogger.Error("Failed to render access denied page", slog.String("error", err.Error()))
		}
	}
}

// HandleSignalTypeManagement renders the signal type management page
func (h *HandlerService) HandleSignalTypeManagement(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	// Get user permissions from cookie
	isnPerms, err := h.AuthService.GetIsnPermsFromCookie(r)
	if err != nil {
		reqLogger.Error("failed to read IsnPerms from cookie", slog.String("error", err.Error()))
		return
	}

	// Convert permissions to ISN list for dropdown (only ISNs where user has write permission)
	var isns []types.IsnDropdown
	isns = make([]types.IsnDropdown, 0, len(isnPerms))
	for isnSlug, perm := range isnPerms {
		// Only show ISNs where user has write permission (can create signal types)
		if perm.Permission == "write" {
			isns = append(isns, types.IsnDropdown{
				Slug:    isnSlug,
				IsInUse: true,
			})
		}
	}

	// Render signal type management page
	component := templates.SignalTypeManagementPage(isns)
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render signal type management page", slog.String("error", err.Error()))
	}
}

// HandleCreateSignalType handles the form submission to create a new signal type
func (h *HandlerService) HandleCreateSignalType(w http.ResponseWriter, r *http.Request) {
	reqLogger := logger.ContextRequestLogger(r.Context())

	// Parse form data
	isnSlug := r.FormValue("isn_slug")
	title := r.FormValue("title")
	schemaURL := r.FormValue("schema_url")
	bumpType := r.FormValue("bump_type")
	readmeURL := r.FormValue("readme_url")
	detail := r.FormValue("detail")

	// Validate user has admin or owner permission
	isnPerm, err := h.AuthService.CheckIsnPermission(r, isnSlug)
	if err != nil {
		h.RenderError(w, r, uierrors.NewUIError(0, err))
		return
	}

	if isnPerm.Permission != "write" {
		h.RenderError(w, r, uierrors.NewUIError(http.StatusForbidden, nil))
		return
	}

	// Validate required fields
	if isnSlug == "" || title == "" || schemaURL == "" || bumpType == "" {
		h.RenderError(w, r, uierrors.NewUIError(http.StatusBadRequest, nil))
		return
	}

	// Get access token from cookie
	accessTokenCookie, err := r.Cookie(config.AccessTokenCookieName)
	if err != nil {
		h.RenderError(w, r, uierrors.NewUIError(http.StatusUnauthorized, err))
		return
	}
	accessToken := accessTokenCookie.Value

	// Prepare request
	createReq := client.CreateSignalTypeRequest{
		SchemaURL: schemaURL,
		Title:     title,
		BumpType:  bumpType,
	}

	// Add optional fields if provided
	if readmeURL != "" {
		createReq.ReadmeURL = &readmeURL
	}
	if detail != "" {
		createReq.Detail = &detail
	}

	// Call the API to create the signal type
	response, err := h.ApiClient.CreateSignalType(accessToken, isnSlug, createReq)
	if err != nil {
		reqLogger.Error("Failed to create signal type", slog.String("error", err.Error()))
		h.RenderError(w, r, err)
		return
	}

	// Success response
	component := templates.SignalTypeCreationSuccess(*response)
	if err := component.Render(r.Context(), w); err != nil {
		reqLogger.Error("Failed to render success message", slog.String("error", err.Error()))
	}
}
